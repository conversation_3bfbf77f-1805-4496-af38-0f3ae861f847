//
//  FriendViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/19.
//

import UIKit
import SnapKit

class FriendViewController: BaseViewController {
    // MARK: - Properties
    private var videoDisplayVC: VideoDisplayCenterViewController?
    private var videoDisplayBottomConstraint: Constraint?
    private var friendEmptyPlaceholderView: FriendEmptyPlaceholderView?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        // 在 super.viewDidLoad() 之前启用完全自定义布局，避免 BaseViewController 添加默认导航栏
        useFullCustomLayout = true
        isTabBarRootViewController = true
        super.viewDidLoad()
        
        // 嵌入视频展示页
        setupVideoDisplay()
    }
    
    // MARK: - Setup
    private func setupVideoDisplay() {
        // 朋友页暂定使用 type = 3，可根据后端定义调整
        let vc = VideoDisplayCenterViewController(videoListType: 3)
        vc.hideNavBackButton = true
        vc.needsTabBarOffset = false // 让父控制器自行处理 TabBar 高度偏移
        addChild(vc)
        view.addSubview(vc.view)

        // 计算自定义 TabBar + 安全区底部高度
        let tabBarHeight: CGFloat
        if isTabBarRootViewController,
           let tabBarController = self.tabBarController as? CustomTabBarController,
           !tabBarController.customTabBar.isHidden {
            tabBarHeight = 44 + view.safeAreaInsets.bottom
        } else {
            tabBarHeight = 0
        }

        vc.view.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            // 记录底部约束，以便后续更新（如旋转、TabBar 显隐等）
            self.videoDisplayBottomConstraint = make.bottom.equalToSuperview().offset(-tabBarHeight).constraint
        }
        vc.didMove(toParent: self)
        self.videoDisplayVC = vc
    }

    // 当安全区或布局变化时，更新底部约束
    override func viewSafeAreaInsetsDidChange() {
        super.viewSafeAreaInsetsDidChange()
        updateVideoDisplayBottomConstraint()
    }

    private func updateVideoDisplayBottomConstraint() {
        guard let bottomConstraint = videoDisplayBottomConstraint else { return }
        let tabBarHeight: CGFloat
        if isTabBarRootViewController,
           let tabBarController = self.tabBarController as? CustomTabBarController,
           !tabBarController.customTabBar.isHidden {
            tabBarHeight = 44 + view.safeAreaInsets.bottom
        } else {
            tabBarHeight = 0
        }
        bottomConstraint.update(offset: -tabBarHeight)
    }
}
